import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import '../config/cloud_functions_config.dart';

class CloudUploadService {
  static CloudUploadService? _instance;
  static CloudUploadService get instance =>
      _instance ??= CloudUploadService._();

  CloudUploadService._();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload file with Cloud Functions processing
  Future<Map<String, dynamic>> uploadFileWithProcessing({
    required File file,
    required String fileName,
    required String uploadedBy,
    String? categoryId,
    Map<String, String>? customMetadata,
    Function(double)? onProgress,
  }) async {
    try {
      debugPrint(
        '🔄 Starting file upload with Cloud Functions processing: $fileName',
      );

      // Step 1: Validate file before upload
      final fileSize = await file.length();
      final contentType = _getContentType(fileName);

      debugPrint('📋 File validation - Size: ${fileSize}B, Type: $contentType');

      final validationResult = await CloudFunctionsConfig.validateFile(
        fileName: fileName,
        contentType: contentType,
        fileSize: fileSize,
      );

      if (validationResult['isValid'] != true) {
        throw Exception(
          'File validation failed: ${validationResult['message']}',
        );
      }

      debugPrint('✅ File validation passed');

      // Step 2: Upload file to Firebase Storage
      final storageRef = _storage.ref().child(
        'documents/user-uploads/$fileName',
      );

      final uploadTask = storageRef.putFile(
        file,
        SettableMetadata(
          contentType: contentType,
          customMetadata: {
            'uploadedBy': uploadedBy,
            'originalName': fileName,
            'uploadTimestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            if (categoryId != null) 'categoryId': categoryId,
            ...?customMetadata,
          },
        ),
      );

      // Monitor upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
        debugPrint(
          '📤 Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
        );
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      final filePath = snapshot.ref.fullPath;

      debugPrint('✅ File uploaded to Storage: $filePath');

      // Step 3: Process file with Cloud Functions
      debugPrint('🔄 Processing file with Cloud Functions...');

      final processingResult = await CloudFunctionsConfig.processFileUpload(
        filePath: filePath,
        fileName: fileName,
        contentType: contentType,
        categoryId: categoryId,
        metadata: {
          'uploadedBy': uploadedBy,
          'downloadUrl': downloadUrl,
          'fileSize': fileSize.toString(),
          ...?customMetadata,
        },
      );

      if (processingResult['success'] != true) {
        // If processing fails, we should still keep the uploaded file
        debugPrint(
          '⚠️ File processing failed but file is uploaded: ${processingResult['message']}',
        );
      } else {
        debugPrint('✅ File processed successfully by Cloud Functions');
      }

      return {
        'success': true,
        'documentId': processingResult['documentId'],
        'filePath': filePath,
        'downloadUrl': downloadUrl,
        'fileName': fileName,
        'contentType': contentType,
        'fileSize': fileSize,
        'processingResult': processingResult,
      };
    } catch (e) {
      debugPrint('❌ File upload with processing failed: $e');
      rethrow;
    }
  }

  /// Upload multiple files with Cloud Functions processing
  Future<List<Map<String, dynamic>>> uploadMultipleFiles({
    required List<File> files,
    required String uploadedBy,
    String? categoryId,
    Map<String, String>? customMetadata,
    Function(int completed, int total)? onProgress,
  }) async {
    try {
      debugPrint('🔄 Starting multiple file upload: ${files.length} files');

      final results = <Map<String, dynamic>>[];

      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        final fileName = path.basename(file.path);

        try {
          final result = await uploadFileWithProcessing(
            file: file,
            fileName: fileName,
            uploadedBy: uploadedBy,
            categoryId: categoryId,
            customMetadata: customMetadata,
          );

          results.add(result);
          onProgress?.call(i + 1, files.length);

          debugPrint('✅ File ${i + 1}/${files.length} uploaded: $fileName');
        } catch (e) {
          debugPrint(
            '❌ Failed to upload file ${i + 1}/${files.length}: $fileName - $e',
          );

          // Add failed result
          results.add({
            'success': false,
            'fileName': fileName,
            'error': e.toString(),
          });
        }
      }

      debugPrint(
        '🎉 Multiple file upload completed: ${results.length} results',
      );
      return results;
    } catch (e) {
      debugPrint('❌ Multiple file upload failed: $e');
      rethrow;
    }
  }

  /// Get content type based on file extension
  String _getContentType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.txt':
        return 'text/plain';
      case '.csv':
        return 'text/csv';
      case '.zip':
        return 'application/zip';
      case '.rar':
        return 'application/x-rar-compressed';
      default:
        return 'application/octet-stream';
    }
  }

  /// Check if file type is allowed
  bool isFileTypeAllowed(String fileName) {
    final allowedExtensions = [
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.txt',
      '.csv',
      '.zip',
      '.rar',
    ];

    final extension = path.extension(fileName).toLowerCase();
    return allowedExtensions.contains(extension);
  }

  /// Check if file size is within limits (15MB)
  bool isFileSizeAllowed(int fileSize) {
    const maxSize = 15 * 1024 * 1024; // 15MB in bytes
    return fileSize <= maxSize;
  }

  /// Get file size in human readable format
  String getFileSizeString(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Delete file from Storage (for cleanup if processing fails)
  Future<void> deleteFile(String filePath) async {
    try {
      await _storage.ref(filePath).delete();
      debugPrint('🗑️ File deleted from Storage: $filePath');
    } catch (e) {
      debugPrint('❌ Failed to delete file from Storage: $e');
    }
  }
}
