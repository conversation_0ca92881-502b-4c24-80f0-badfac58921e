{"version": 3, "names": ["_findSuggestion", "require", "OptionValidator", "constructor", "descriptor", "validateTopLevelOptions", "options", "TopLevelOptionShape", "validOptionNames", "Object", "keys", "option", "includes", "Error", "formatMessage", "findSuggestion", "validateBooleanOption", "name", "value", "defaultValue", "undefined", "invariant", "validateStringOption", "condition", "message", "exports"], "sources": ["../src/validator.ts"], "sourcesContent": ["import { findSuggestion } from \"./find-suggestion.ts\";\n\nexport class OptionValidator {\n  declare descriptor: string;\n  constructor(descriptor: string) {\n    this.descriptor = descriptor;\n  }\n\n  /**\n   * Validate if the given `options` follow the name of keys defined in the `TopLevelOptionShape`\n   *\n   * @param {Object} options\n   * @param {Object} TopLevelOptionShape\n   *   An object with all the valid key names that `options` should be allowed to have\n   *   The property values of `TopLevelOptionShape` can be arbitrary\n   * @memberof OptionValidator\n   */\n  validateTopLevelOptions(options: object, TopLevelOptionShape: object): void {\n    const validOptionNames = Object.keys(TopLevelOptionShape);\n    for (const option of Object.keys(options)) {\n      if (!validOptionNames.includes(option)) {\n        throw new Error(\n          this.formatMessage(`'${option}' is not a valid top-level option.\n- Did you mean '${findSuggestion(option, validOptionNames)}'?`),\n        );\n      }\n    }\n  }\n\n  // note: we do not consider rewrite them to high order functions\n  // until we have to support `validateNumberOption`.\n  validateBooleanOption<T extends boolean>(\n    name: string,\n    value?: boolean,\n    defaultValue?: T,\n  ): boolean | T {\n    if (value === undefined) {\n      return defaultValue;\n    } else {\n      this.invariant(\n        typeof value === \"boolean\",\n        `'${name}' option must be a boolean.`,\n      );\n    }\n    return value;\n  }\n\n  validateStringOption<T extends string>(\n    name: string,\n    value?: string,\n    defaultValue?: T,\n  ): string | T {\n    if (value === undefined) {\n      return defaultValue;\n    } else {\n      this.invariant(\n        typeof value === \"string\",\n        `'${name}' option must be a string.`,\n      );\n    }\n    return value;\n  }\n  /**\n   * A helper interface copied from the `invariant` npm package.\n   * It throws given `message` when `condition` is not met\n   *\n   * @param {boolean} condition\n   * @param {string} message\n   * @memberof OptionValidator\n   */\n  invariant(condition: boolean, message: string): void {\n    if (!condition) {\n      throw new Error(this.formatMessage(message));\n    }\n  }\n\n  formatMessage(message: string): string {\n    return `${this.descriptor}: ${message}`;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,eAAA,GAAAC,OAAA;AAEO,MAAMC,eAAe,CAAC;EAE3BC,WAAWA,CAACC,UAAkB,EAAE;IAC9B,IAAI,CAACA,UAAU,GAAGA,UAAU;EAC9B;EAWAC,uBAAuBA,CAACC,OAAe,EAAEC,mBAA2B,EAAQ;IAC1E,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACH,mBAAmB,CAAC;IACzD,KAAK,MAAMI,MAAM,IAAIF,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAE;MACzC,IAAI,CAACE,gBAAgB,CAACI,QAAQ,CAACD,MAAM,CAAC,EAAE;QACtC,MAAM,IAAIE,KAAK,CACb,IAAI,CAACC,aAAa,CAAC,IAAIH,MAAM;AACvC,kBAAkB,IAAAI,8BAAc,EAACJ,MAAM,EAAEH,gBAAgB,CAAC,IAAI,CACtD,CAAC;MACH;IACF;EACF;EAIAQ,qBAAqBA,CACnBC,IAAY,EACZC,KAAe,EACfC,YAAgB,EACH;IACb,IAAID,KAAK,KAAKE,SAAS,EAAE;MACvB,OAAOD,YAAY;IACrB,CAAC,MAAM;MACL,IAAI,CAACE,SAAS,CACZ,OAAOH,KAAK,KAAK,SAAS,EAC1B,IAAID,IAAI,6BACV,CAAC;IACH;IACA,OAAOC,KAAK;EACd;EAEAI,oBAAoBA,CAClBL,IAAY,EACZC,KAAc,EACdC,YAAgB,EACJ;IACZ,IAAID,KAAK,KAAKE,SAAS,EAAE;MACvB,OAAOD,YAAY;IACrB,CAAC,MAAM;MACL,IAAI,CAACE,SAAS,CACZ,OAAOH,KAAK,KAAK,QAAQ,EACzB,IAAID,IAAI,4BACV,CAAC;IACH;IACA,OAAOC,KAAK;EACd;EASAG,SAASA,CAACE,SAAkB,EAAEC,OAAe,EAAQ;IACnD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIV,KAAK,CAAC,IAAI,CAACC,aAAa,CAACU,OAAO,CAAC,CAAC;IAC9C;EACF;EAEAV,aAAaA,CAACU,OAAe,EAAU;IACrC,OAAO,GAAG,IAAI,CAACpB,UAAU,KAAKoB,OAAO,EAAE;EACzC;AACF;AAACC,OAAA,CAAAvB,eAAA,GAAAA,eAAA", "ignoreList": []}